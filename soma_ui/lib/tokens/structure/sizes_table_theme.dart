import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:soma_core/modules/catalog/models/product/index.dart';
import 'package:soma_core/modules/checkout/models/orderForm/table_measures.dart';

class SizesTableTheme {
  final SizesTableFactory sizesTable;
  final bool iSasyncSizesTable;
  final ImageProvider bodyMeasuresImage;
  final ImageProvider shoesMeasuresImage;
  final String? tapeMeasureLink;
  final double? tableRowHeight;
  final double? tableHeaderHeight;
  final String? tableTitle;
  final String? tableSubTitle;
  final bool hasMultipleSizeSelection;
  final String? tableImageTitle;
  final String? tableImageSubTitle;
  final bool roundedNumerationStyle;
  final bool hasExchangeContainer;
  final bool hasNeedHelpContainer;
  final String? jewelrySizesImageOverride;
  final bool hideTitleWhenJewelryImage;
  final String? sizesTableBottomSheetHeaderTitle;
  final String? sizesTableBottomSheetHeaderSubtitle;
  final Map<String, ImageProvider>? brandImages;
  final Map<String, ImageProvider>? measuresImages;
  final List<BodyMeasure>? bodyMeasuresInstructions;
  final List<BodyMeasure>? bodyMeasuresInstructionsKids;
  final List<BodyMeasure>? bodyMeasuresInstructionsMen;
  final bool? hideCloseButton;
  final bool? hideButton;
  final bool? showDragHandling;

  const SizesTableTheme({
    required this.sizesTable,
    this.iSasyncSizesTable = false,
    this.bodyMeasuresImage =
        const AssetImage('assets/images/product_measures.png'),
    this.shoesMeasuresImage =
        const AssetImage('assets/images/shoes_measures.png'),
    this.tapeMeasureLink,
    this.tableRowHeight,
    this.tableHeaderHeight,
    this.tableTitle,
    this.tableSubTitle,
    this.hasMultipleSizeSelection = false,
    this.tableImageTitle,
    this.tableImageSubTitle,
    this.roundedNumerationStyle = false,
    this.hasExchangeContainer = false,
    this.hasNeedHelpContainer = false,
    this.jewelrySizesImageOverride,
    this.hideTitleWhenJewelryImage = false,
    this.sizesTableBottomSheetHeaderTitle,
    this.sizesTableBottomSheetHeaderSubtitle,
    this.brandImages,
    this.measuresImages,
    this.bodyMeasuresInstructions,
    this.bodyMeasuresInstructionsKids,
    this.bodyMeasuresInstructionsMen,
    this.hideCloseButton = true,
    this.hideButton = true,
    this.showDragHandling = true,
  });
}

enum SizesTableType {
  simple,
  detailed,
  jewelry,
  measurements,
  completed,
  clean,
  shoes,
  async,
}

abstract class SizesTableFactory {
  SizesTable forProduct(Product product, {TableMeasures? tableMeasures});
  bool hasSizesTable(Product product);
  Future<SizesTable>? forProductAsync(Product product,
      {TableMeasures? tableMeasures});
}

class SizeMeasures {
  final String size;
  final Map<int, dynamic> measures;

  const SizeMeasures({
    required this.size,
    required this.measures,
  });
}

class BodyMeasure {
  final int id;
  final String name;
  final String nameOnSizesTable;
  final String? measuringInstructions;

  const BodyMeasure({
    required this.id,
    required this.name,
    String? nameOnSizesTable,
    this.measuringInstructions,
  }) : nameOnSizesTable = nameOnSizesTable ?? name;
}

class SizesTable {
  final SizesTableType type;
  final List<BodyMeasure> bodyMeasures;
  final List<SizeMeasures> sizeMeasures;
  final List<BodyMeasure>? measuringInstructions;

  const SizesTable({
    this.type = SizesTableType.simple,
    required this.bodyMeasures,
    required this.sizeMeasures,
    this.measuringInstructions,
  });
}

// Constantes padrão para instruções de medidas corporais
const List<BodyMeasure> defaultBodyMeasuresInstructions = [
  BodyMeasure(
    id: 1,
    name: 'Tórax',
    measuringInstructions: 'Contorne abaixo da axila e acima do busto.',
  ),
  BodyMeasure(
    id: 2,
    name: 'Busto',
    measuringInstructions:
        'Contorne o busto passando pela altura do seio. A fita deve estar folgada.',
  ),
  BodyMeasure(
    id: 3,
    name: 'Cintura',
    measuringInstructions:
        'Contorne a cintura colocando a fita na parte mais fina.',
  ),
  BodyMeasure(
    id: 4,
    name: 'Cintura baixa',
    measuringInstructions:
        'Contorne na linha do umbigo, aproximadamente 4 cm abaixo da linha da cintura.',
  ),
  BodyMeasure(
    id: 5,
    name: 'Quadril',
    measuringInstructions:
        'Contornar na parte do quadril com a maior circunferência.',
  ),
  BodyMeasure(
    id: 6,
    name: 'Coxa Total',
    measuringInstructions:
        'Contorne a parte mais larga da coxa, aproximadamente 2cm abaixo da virilha.',
  ),
  BodyMeasure(
    id: 7,
    name: 'Comprimento da cintura até o chão',
    measuringInstructions:
        'Meça da parte mais fina da cintura até a planta do pé na frente do corpo.',
  ),
  BodyMeasure(
    id: 8,
    name: 'Comprimento do braço',
    measuringInstructions: 'Meça do canto do ombro até a dobra do punho.',
  ),
];

const List<BodyMeasure> defaultBodyMeasuresInstructionsKids = [
  BodyMeasure(
    id: 1,
    name: 'Tórax',
    measuringInstructions:
        'Contornar seu tórax e respire normalmente. Observe as variações enquanto respira e registre o maior número.',
  ),
  BodyMeasure(
    id: 2,
    name: 'Busto',
    measuringInstructions:
        'Contorne a cintura colocando a fita na parte mais fina. Ao inclinar um pouco para o lado, na frente de um espelho, você deve observar a formação de um vinco, essa é a localização natural da cintura.',
  ),
  BodyMeasure(
    id: 3,
    name: 'Quadril',
    measuringInstructions:
        'Contornar na parte do quadril com a maior circunferência, passando paralelamente pela parte mais alta dos glúteos.',
  ),
  BodyMeasure(
    id: 4,
    name: 'Cintura baixa',
    measuringInstructions: 'Meça verticalmente dos pés à cabeça.',
  )
];

const List<BodyMeasure> defaultBodyMeasuresInstructionsMen = [
  BodyMeasure(
    id: 1,
    name: 'Tórax',
    measuringInstructions:
        'Contornar seu tórax e respire normalmente. Observe as variações enquanto respira e registre o maior número.',
  ),
  BodyMeasure(
    id: 2,
    name: 'Cintura',
    measuringInstructions:
        'Contorne a cintura colocando a fita na parte mais fina. Ao inclinar um pouco para o lado, na frente de um espelho, você deve observar a formação de um vinco, essa é a localização natural da cintura.',
  ),
  BodyMeasure(
    id: 3,
    name: 'Quadril',
    measuringInstructions:
        'Contornar na parte do quadril com a maior circunferência, passando paralelamente pela parte mais alta dos glúteos.',
  ),
  BodyMeasure(
    id: 4,
    name: 'Comprimento do braço',
    measuringInstructions: 'Meça verticalmente dos pés à cabeça.',
  ),
  BodyMeasure(
    id: 5,
    name: 'Coxa Total',
    measuringInstructions:
        'Contorne a parte mais larga da coxa, um pouco abaixo dos glúteos.',
  )
];

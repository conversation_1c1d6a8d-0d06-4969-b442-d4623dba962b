import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:soma_core/modules/catalog/models/product/index.dart';
import 'package:soma_core/modules/checkout/models/orderForm/table_measures.dart';

class SizesTableTheme {
  final SizesTableFactory sizesTable;
  final bool iSasyncSizesTable;
  final ImageProvider bodyMeasuresImage;
  final ImageProvider shoesMeasuresImage;
  final String? tapeMeasureLink;
  final double? tableRowHeight;
  final double? tableHeaderHeight;
  final String? tableTitle;
  final String? tableSubTitle;
  final bool hasMultipleSizeSelection;
  final String? tableImageTitle;
  final String? tableImageSubTitle;
  final bool roundedNumerationStyle;
  final bool hasExchangeContainer;
  final bool hasNeedHelpContainer;
  final String? jewelrySizesImageOverride;
  final bool hideTitleWhenJewelryImage;
  final String? sizesTableBottomSheetHeaderTitle;
  final Map<String, ImageProvider>? brandImages;
  final Map<String, ImageProvider>? measuresImages;
  final bool? hideCloseButton;
  final bool? hideButton;
  final bool? showDragHandling;

  const SizesTableTheme({
    required this.sizesTable,
    this.iSasyncSizesTable = false,
    this.bodyMeasuresImage =
        const AssetImage('assets/images/product_measures.png'),
    this.shoesMeasuresImage =
        const AssetImage('assets/images/shoes_measures.png'),
    this.tapeMeasureLink,
    this.tableRowHeight,
    this.tableHeaderHeight,
    this.tableTitle,
    this.tableSubTitle,
    this.hasMultipleSizeSelection = false,
    this.tableImageTitle,
    this.tableImageSubTitle,
    this.roundedNumerationStyle = false,
    this.hasExchangeContainer = false,
    this.hasNeedHelpContainer = false,
    this.jewelrySizesImageOverride,
    this.hideTitleWhenJewelryImage = false,
    this.sizesTableBottomSheetHeaderTitle,
    this.brandImages,
    this.measuresImages,
    this.hideCloseButton = true,
    this.hideButton = true,
    this.showDragHandling = true,
  });
}

enum SizesTableType {
  simple,
  detailed,
  jewelry,
  measurements,
  completed,
  clean,
  shoes,
  async,
}

abstract class SizesTableFactory {
  SizesTable forProduct(Product product, {TableMeasures? tableMeasures});

  bool hasSizesTable(Product product);
  
  Future<SizesTable>? forProductAsync(Product product,
      {TableMeasures? tableMeasures});
}

class SizeMeasures {
  final String size;
  final Map<int, dynamic> measures;

  const SizeMeasures({
    required this.size,
    required this.measures,
  });
}

class BodyMeasure {
  final int id;
  final String name;
  final String nameOnSizesTable;
  final String? measuringInstructions;

  const BodyMeasure({
    required this.id,
    required this.name,
    String? nameOnSizesTable,
    this.measuringInstructions,
  }) : nameOnSizesTable = nameOnSizesTable ?? name;
}

class SizesTable {
  final SizesTableType type;
  final List<BodyMeasure> bodyMeasures;
  final List<SizeMeasures> sizeMeasures;
  final List<BodyMeasure>? measuringInstructions;

  const SizesTable({
    this.type = SizesTableType.simple,
    required this.bodyMeasures,
    required this.sizeMeasures,
    this.measuringInstructions,
  });
}

import 'package:flutter/material.dart';

import '../../soma_ui.dart';

class SMMeasurementSizesTable extends StatefulWidget {
  final SizesTable sizesTable;
  final ImageProvider bodyMeasuresImage;
  final double? tableRowHeight;
  final double? tableHeaderHeight;

  const SMMeasurementSizesTable({
    super.key,
    required this.sizesTable,
    required this.bodyMeasuresImage,
    this.tableRowHeight,
    this.tableHeaderHeight,
  });

  @override
  State<SMMeasurementSizesTable> createState() =>
      _SMMeasurementSizesTableState();
}

class _SMMeasurementSizesTableState extends State<SMMeasurementSizesTable>
    with DesignTokensStateMixin {
  List<String?> _selectedSizes = [];
  List<SizeMeasures?> _selectedSizeMeasures = [];
  bool? _isSizeMeasuresEmpty;

  @override
  void initState() {
    super.initState();

    _isSizeMeasuresEmpty = widget.sizesTable.sizeMeasures.isEmpty;
  }

  void _selectSize(String? size) {
    if (size != null && !_selectedSizes.contains(size)) {
      if (_selectedSizes.length > 4) {
        setState(() {
          _selectedSizes.removeAt(0);
        });
      }

      setState(() {
        _selectedSizes.add(size);
      });
    } else {
      setState(() {
        _selectedSizes.remove(size);
      });
    }

    List<SizeMeasures> availableSelectedSize = widget.sizesTable.sizeMeasures
        .where((element) => _selectedSizes.contains(element.size))
        .toList();

    setState(() {
      _selectedSizeMeasures = availableSelectedSize;
      _selectedSizes = availableSelectedSize.map((e) => e.size).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return _isSizeMeasuresEmpty == true
        ? Padding(
            padding: EdgeInsets.only(right: spacingInset.xxl),
            child: const SMMainContent(
              subtitle: 'Desculpe, ainda não temos as medidas pra esta peça.',
            ),
          )
        : _buildTableContent(_selectedSizes);
  }

  Widget _buildTableContent(selectedSizes) {
    return Column(
      children: [
        SizedBox(
          height: spacingStack.lg - spacingStack.md,
        ),
        if (!_selectedSizeMeasures.isNotEmpty && !_selectedSizes.isNotEmpty)
          const _EmptySizeSelector(),
        _SizeSelector(
          sizes: widget.sizesTable.sizeMeasures.map((s) => s.size).toList(),
          onSelect: _selectSize,
          selected: selectedSizes,
        ),
        SizedBox(height: spacingStack.sm),
        Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: colors.neutral.light1,
            ),
            borderRadius: BorderRadius.circular(10),
          ),
          width: double.infinity,
          child: Center(
            child: SMImage(
              image: widget.bodyMeasuresImage,
              imageWidth: MediaQuery.of(context).size.width / 1.5,
            ),
          ),
        ),
        if (!_selectedSizeMeasures.isNotEmpty && !_selectedSizes.isNotEmpty)
          const SizedBox(height: 280),
        if (_selectedSizeMeasures.isNotEmpty && _selectedSizes.isNotEmpty) ...[
          SizedBox(height: spacingStack.sm),
          _SizesTable(
            bodyMeasures: widget.sizesTable.bodyMeasures,
            selectedSizeMeasures: _selectedSizeMeasures,
            selectedSizes: _selectedSizes,
            rowHeight: widget.tableRowHeight,
            headerHeight: 40,
          ),
        ],
      ],
    );
  }
}

class _SizeSelector extends StatelessWidget {
  final List<String> sizes;
  final ValueChanged<String> onSelect;
  final List<String?> selected;

  const _SizeSelector({
    required this.sizes,
    required this.onSelect,
    required this.selected,
  });

  @override
  Widget build(BuildContext context) {
    final tokens = context.designTokens;

    return Row(
      mainAxisAlignment: sizes.length == 1
          ? MainAxisAlignment.center
          : MainAxisAlignment.spaceAround,
      children: [
        for (final size in sizes) ...[
          SMSizeButton(
            textVariant: TextVariant.upperCase,
            text: TextUtils.verifySizeText(size),
            height: 40,
            backgroundColor:
                selected.contains(size) ? tokens.colors.neutral.pure2 : null,
            textColor:
                selected.contains(size) ? tokens.colors.neutral.pure1 : null,
            onTap: () => onSelect(size),
          ),
        ]
      ],
    );
  }
}

class _SizesTable extends StatefulWidget {
  final List<BodyMeasure> bodyMeasures;
  final List<SizeMeasures?> selectedSizeMeasures;
  final List<String?> selectedSizes;
  final double? rowHeight;
  final double? headerHeight;

  const _SizesTable({
    required this.bodyMeasures,
    required this.selectedSizeMeasures,
    required this.selectedSizes,
    this.rowHeight,
    this.headerHeight,
  });

  @override
  State<_SizesTable> createState() => _SizesTableState();
}

class _SizesTableState extends State<_SizesTable> with DesignTokensStateMixin {
  int? selectedColumn;
  int? selectedRow;

  @override
  Widget build(BuildContext context) {
    String columName(index) {
      final splittedName =
          widget.bodyMeasures[index].nameOnSizesTable.split(' ');
      return splittedName[splittedName.length - 1].toLowerCase();
    }

    String rowName(index, indexBodyMeasure) {
      final measure = widget.selectedSizeMeasures[index]!
          .measures[widget.bodyMeasures[indexBodyMeasure].id]!;
      if (measure is num) {
        return '${measure.round()} cm';
      } else {
        return '$measure cm';
      }
    }

    Color colorCell(indexColumn, indexRow) {
      if (indexColumn == selectedColumn || indexRow == selectedRow) {
        return colors.feedback.lightSuccess;
      }

      if (indexRow % 2 == 0) {
        return colors.neutral.light1;
      }

      return Colors.transparent;
    }

    return DefaultTextStyle(
      style: typography.typeStyles.body.copyWith(
        color: colors.typography.pure2,
        fontWeight: FontWeight.w500,
      ),
      child: SizedBox(
        height: MediaQuery.of(context).size.height * 0.7,
        child: Table(
          border: TableBorder.all(
            color: colors.neutral.light1,
          ),
          defaultVerticalAlignment: TableCellVerticalAlignment.middle,
          children: [
            _buildTableHeader(),
            for (int indexBodyMeasure = 0;
                indexBodyMeasure < widget.bodyMeasures.length;
                indexBodyMeasure++)
              TableRow(
                children: [
                  TableRowInkWell(
                    onTap: () {
                      setState(() {
                        selectedRow = indexBodyMeasure;
                      });
                    },
                    child: _buildCell(
                      height: 40,
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              columName(indexBodyMeasure),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                      color: colorCell(-1, indexBodyMeasure),
                      textAlignment: Alignment.centerLeft,
                    ),
                  ),
                  for (int i = 0; i < widget.selectedSizeMeasures.length; i++)
                    TableRowInkWell(
                      onTap: () {
                        setState(() {
                          selectedColumn = i;
                          selectedRow = indexBodyMeasure;
                        });
                      },
                      child: _buildCell(
                        height: 40,
                        Text(
                          overflow: TextOverflow.ellipsis,
                          rowName(i, indexBodyMeasure),
                        ),
                        color: colorCell(i, indexBodyMeasure),
                        cellAlignment: TableCellVerticalAlignment.fill,
                      ),
                    ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  TableRow _buildTableHeader() {
    return TableRow(
      children: [
        _buildCell(
          Text('',
              overflow: TextOverflow.ellipsis,
              style: typeStyles.subtitle.copyWith(
                fontWeight: FontWeight.bold,
              )),
          textAlignment: Alignment.centerLeft,
          height: widget.headerHeight,
        ),
        for (int i = 0; i < widget.selectedSizes.length; i++)
          _buildCell(
            Text(
              overflow: TextOverflow.ellipsis,
              widget.selectedSizes[i]!,
              style: typeStyles.body.copyWith(
                fontWeight: FontWeight.w400,
                color: colors.neutral.pure2,
              ),
            ),
            height: widget.headerHeight,
          ),
      ],
    );
  }

  Widget _buildCell(
    Widget content, {
    Alignment textAlignment = Alignment.center,
    Color? color,
    TableCellVerticalAlignment? cellAlignment,
    double? height,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: spacingInline.sm),
      constraints: BoxConstraints.tightFor(height: height ?? widget.rowHeight),
      color: color ?? Colors.transparent,
      alignment: textAlignment,
      child: content,
    );
  }
}

class _EmptySizeSelector extends StatelessWidget {
  const _EmptySizeSelector();

  @override
  Widget build(BuildContext context) {
    final tokens = context.designTokens;

    return Container(
      width: double.infinity,
      height: 35,
      margin: const EdgeInsets.only(top: 20, bottom: 20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: tokens.colors.feedback.pureSuccess,
      ),
      child: Center(
        child: Text(
          'selecione um tamanho pra começar',
          style: tokens.typography.typeStyles.body.copyWith(
            color: tokens.colors.neutral.pure1,
          ),
        ),
      ),
    );
  }
}
